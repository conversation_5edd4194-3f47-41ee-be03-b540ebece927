# Firebase Deployment Guide for Flare Frontend

## 🔥 Deploy to Firebase Hosting

### Step 1: Install Firebase CLI
```bash
npm install -g firebase-tools
```

### Step 2: Login to Firebase
```bash
firebase login
```

### Step 3: Create Firebase Project
1. **Go to [Firebase Console](https://console.firebase.google.com/)**
2. **Click "Create a project"**
3. **Project name**: `flare-portal-app` (or your preferred name)
4. **Enable Google Analytics** (optional)
5. **Create project**

### Step 4: Initialize Firebase in Project
```bash
cd Flare/vite-project
firebase init hosting
```

**Configuration options:**
- **Use an existing project**: Select your created project
- **Public directory**: `dist`
- **Single-page app**: `Yes`
- **Automatic builds**: `No` (we'll build manually)
- **Overwrite index.html**: `No`

### Step 5: Configure Environment Variables
Update `.env.production` with your Render backend URL:
```env
VITE_API_BASE_URL=https://your-render-backend-url.onrender.com
```

### Step 6: Build and Deploy
```bash
# Build for production
npm run build

# Deploy to Firebase
firebase deploy
```

### Step 7: Update Backend CORS
After deployment, update your Render backend environment variables:
```
FRONTEND_URL=https://your-firebase-app.web.app
```

## 🔧 Deployment Commands

### Full Deployment Process
```bash
# 1. Navigate to frontend directory
cd Flare/vite-project

# 2. Install dependencies (if needed)
npm install

# 3. Update environment variables
# Edit .env.production with your Render backend URL

# 4. Build the project
npm run build

# 5. Deploy to Firebase
firebase deploy

# 6. Optional: Deploy with custom message
firebase deploy -m "Deploy version 1.0"
```

### Quick Redeploy
```bash
npm run build && firebase deploy
```

## 🌐 Custom Domain (Optional)

### Step 1: Add Custom Domain
1. **Go to Firebase Console** → Your Project → Hosting
2. **Click "Add custom domain"**
3. **Enter your domain** (e.g., `flare-portal.com`)
4. **Follow verification steps**

### Step 2: Configure DNS
Add these records to your domain provider:
```
Type: A
Name: @
Value: *************

Type: A  
Name: @
Value: **************
```

For subdomain (www):
```
Type: CNAME
Name: www
Value: your-firebase-app.web.app
```

## 📊 Environment Configuration

### Development (.env)
```env
VITE_API_BASE_URL=http://localhost:3001
```

### Production (.env.production)
```env
VITE_API_BASE_URL=https://your-render-backend-url.onrender.com
```

### Build-time Variables
Vite automatically uses the correct environment file:
- **Development**: `.env` and `.env.local`
- **Production**: `.env.production` and `.env.production.local`

## 🔍 Testing Deployment

### Test Frontend
1. **Open your Firebase URL**: `https://your-firebase-app.web.app`
2. **Test wallet connection flow**
3. **Check browser console** for any errors
4. **Verify API calls** go to Render backend

### Test Integration
```bash
# Test from deployed frontend
# 1. Open Firebase app in browser
# 2. Click "Connect to Wallet"
# 3. Select a wallet
# 4. Enter test data
# 5. Submit form
# 6. Check Render backend logs
```

## 🐛 Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules dist
   npm install
   npm run build
   ```

2. **API Calls Fail**
   - Check `.env.production` has correct backend URL
   - Verify CORS settings in backend
   - Check network tab in browser dev tools

3. **Firebase Deploy Fails**
   ```bash
   # Re-login to Firebase
   firebase logout
   firebase login
   firebase deploy
   ```

4. **Environment Variables Not Working**
   - Ensure variables start with `VITE_`
   - Rebuild after changing environment files
   - Check `import.meta.env.VITE_API_BASE_URL` in code

### Debug Commands
```bash
# Check Firebase projects
firebase projects:list

# Check hosting status
firebase hosting:sites:list

# View deployment history
firebase hosting:releases:list

# Test locally before deploy
npm run preview
```

## 📈 Performance Optimization

### Build Optimization
```bash
# Analyze bundle size
npm run build -- --analyze

# Preview production build locally
npm run preview
```

### Firebase Hosting Features
- **Global CDN**: Automatic worldwide distribution
- **HTTP/2**: Faster loading
- **Gzip Compression**: Automatic compression
- **SSL Certificate**: Automatic HTTPS
- **Cache Control**: Configured in `firebase.json`

## 🔒 Security

### Environment Variables
- **Never commit** `.env.production` with real URLs
- **Use Firebase environment config** for sensitive data
- **Validate all API responses** in frontend

### HTTPS
- **Automatic HTTPS** on Firebase Hosting
- **SSL certificates** managed automatically
- **Secure headers** configured

## 📝 Deployment Checklist

- [ ] Firebase CLI installed
- [ ] Firebase project created
- [ ] Firebase initialized in project
- [ ] Environment variables configured
- [ ] Backend deployed to Render first
- [ ] Frontend built successfully
- [ ] Firebase deployment successful
- [ ] Custom domain configured (optional)
- [ ] CORS updated in backend
- [ ] End-to-end testing completed

## 🔗 Useful Links

- [Firebase Hosting Documentation](https://firebase.google.com/docs/hosting)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)
- [Custom Domains](https://firebase.google.com/docs/hosting/custom-domain)
