import React from 'react';

const ManagementSection = () => {
  const proposals = [
    { id: 'FIP-13', title: 'Add Custom Feeds to FTSO', status: 'Accepted' },
    { id: 'FIP-12', title: 'Add Support for the Flare Data Connector', status: 'Accepted' },
    { id: 'FIP-11', title: 'Optimize Rewarding Structure and Adjust Protocol Parameters', status: 'Accepted' },
    { id: 'FIP-10', title: 'Add an Incentive Structure for Participating in All Protocols', status: 'Accepted' },
    { id: 'FIP-9', title: 'Introduce FLR Protocol Emissions', status: 'Accepted' },
    { id: 'FIP-8', title: 'Update FTSO Data Feeds and Define Process to Add New Ones', status: 'Accepted' },
    { id: 'FIP-7', title: 'Add Support for FTSO Fast Updates', status: 'Accepted' },
    { id: 'FIP-6', title: 'Add Support for the Flare Systems Protocol and FTSO Scaling', status: 'Accepted' },
    { id: 'FIP-5', title: 'Update Services, Limits, and Rewards Required for Staking', status: 'Accepted' },
    { id: 'FIP-4', title: 'Update FTSO price pairs', status: 'Accepted' }
  ];

  return (
    <main className="flex-1 bg-gray-50 py-8 md:py-20">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-2xl md:text-4xl font-bold text-gray-900 mb-8 md:mb-12">
            Welcome to the Flare portal!
          </h1>

          <div className="bg-gray-800 text-white p-4 md:p-8 rounded-lg max-w-4xl mx-auto">
            <div className="space-y-4 md:space-y-6 text-left">
              <p className="text-sm md:text-base leading-relaxed">
                Here, you can manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers
                and thereby contribute to the networks' decentralization and stability. You can also participate in governance voting from this
                portal.
              </p>

              <p className="text-sm md:text-base leading-relaxed">
                Please start by clicking on the 'Connect to Wallet' button, select your wallet and follow the instructions. This page is compatible
                for both desktop and mobile browsers. For technical support, please make a request in the General room on our{' '}
                <a href="#" className="text-red-400 hover:text-red-300 underline">
                  Discord
                </a>{' '}
                or on our{' '}
                <a href="#" className="text-red-400 hover:text-red-300 underline">
                  Telegram
                </a>.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mt-6 md:mt-9">
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-4 md:mb-6">
            <h1 className="text-xl md:text-2xl font-bold text-gray-900">Management Proposals</h1>
            <svg className="w-4 h-4 md:w-5 md:h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>

          <div className="space-y-3 md:space-y-4">
            {proposals.map((proposal) => (
              <div key={proposal.id} className="bg-white rounded-lg border border-gray-200 p-3 md:p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 md:space-x-4 flex-1 min-w-0">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs font-bold">⚡</span>
                    </div>
                    <div className="min-w-0 flex-1">
                      <span className="text-xs md:text-sm font-medium text-gray-900 block truncate">
                        {proposal.id}: {proposal.title}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center flex-shrink-0 ml-2">
                    <span className="bg-gray-800 text-white text-xs px-2 md:px-3 py-1 rounded-full">
                      {proposal.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
};

export default ManagementSection;
