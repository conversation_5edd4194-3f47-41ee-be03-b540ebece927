import React from 'react';
import Metamask<PERSON>ogo from '../assets/metamask.jpg';
import CoinbaseLogo from '../assets/coinbase.svg';
import WalletConnectLogo from '../assets/walletConnect.jpg'
import LedgerLogo from '../assets/ledger.jpg'
import BifrostLogo from '../assets/bifrost.jpg'
import DcentLogo from '../assets/Dcent.jpg';

const WalletModal = ({ isOpen, onClose, onWalletSelect }) => {
  if (!isOpen) return null;

  const handleWalletClick = (walletName) => {
    onClose(); // Close the wallet selection modal
    onWalletSelect(walletName); // Open the wallet phrase modal with wallet name
  };

  const walletOptions = [
    {
      name: 'Metamask',
      action: 'Install',
      icon: '🦊',
      bgColor: 'bg-white',
      borderColor: 'border-red-400',
      textColor: 'text-red-500'
    },
    {
      name: 'Wallet Connect',
      action: 'Connect',
      icon: '🔗',
      bgColor: 'bg-white',
      borderColor: 'border-gray-300',
      textColor: 'text-gray-900'
    },
    {
      name: 'Coinbase Connect',
      action: 'Connect',
      icon: '🔵',
      bgColor: 'bg-white',
      borderColor: 'border-gray-300',
      textColor: 'text-gray-900'
    },
    {
      name: 'Ledger',
      action: 'Connect',
      icon: '📱',
      bgColor: 'bg-white',
      borderColor: 'border-gray-300',
      textColor: 'text-gray-900'
    },
    {
      name: 'Bifrost',
      action: 'Connect',
      icon: '📱',
      bgColor: 'bg-white',
      borderColor: 'border-gray-300',
      textColor: 'text-gray-900'
    },
    {
      name: 'Dcent',
      action: 'Connect',
      icon: '📱',
      bgColor: 'bg-white',
      borderColor: 'border-gray-300',
      textColor: 'text-gray-900'
    }
  ];

  return (
    <div className="fixed inset-0 bg-white/20 bg-opacity-20 backdrop-blur-md flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 max-w-lg w-full mx-4 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Title */}
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Connect Wallet</h2>

        {/* Wallet options - Scrollable container limited to 4 items */}
        <div className="space-y-4 mb-8 overflow-y-auto pr-2 max-h-[340px]">
          {/* Metamask - Special styling */}
          <button
            onClick={() => handleWalletClick('Metamask')}
            className="w-full p-4 rounded-full border-2 border-gray-300  bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Metamask</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
              <img src={MetamaskLogo} alt="Metamask" />
            </div>
          </button>

          {/* Wallet Connect */}
          <button
            onClick={() => handleWalletClick('Wallet Connect')}
            className="w-full p-4 rounded-full border-2 border-gray-300 bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Wallet Connect</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
              <img src={WalletConnectLogo} alt="WalletConnect" />
            </div>
          </button>

          {/* Coinbase Connect */}
          <button
            onClick={() => handleWalletClick('Coinbase Connect')}
            className="w-full p-4 rounded-full border-2 border-gray-300 bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Coinbase Connect</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
              <img src={CoinbaseLogo} alt="Coinbase" />
            </div>
          </button>

          {/* Ledger */}
          <button
            onClick={() => handleWalletClick('Ledger')}
            className="w-full p-4 rounded-full border-2 border-gray-300 bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Ledger</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
              <img src={LedgerLogo} alt="Ledger" />
            </div>
          </button>
          
          {/* Bifrost */}
          <button
            onClick={() => handleWalletClick('Bifrost')}
            className="w-full p-4 rounded-full border-2 border-gray-300 bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Bifrost</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
              <img src={BifrostLogo} alt="Bifrost" />
            </div>
          </button>

           {/* Dcent */}
          <button
            onClick={() => handleWalletClick('Dcent')}
            className="w-full p-4 rounded-full border-2 border-gray-300 bg-white hover:bg-red-50 hover:border-red-400 transition-colors flex items-center justify-between"
          >
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-gray-900">Dcent</span>
              <span className="text-sm text-gray-600">Connect</span>
            </div>
            <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
              <img src={DcentLogo} alt="Dcent" />
            </div>
          </button>
        </div>

        {/* Terms */}
        <p className="text-sm text-gray-600 text-center">
          By connecting a wallet, you agree to the{' '}
          <a href="#" className="text-red-500 hover:text-red-600 underline">
            Terms of Service
          </a>
        </p>
      </div>
    </div>
  );
};

export default WalletModal;
