import React from 'react';

const WelcomeSection = ({ activeTab }) => {
  // You can customize content based on activeTab if needed
  const getTabTitle = () => {
    switch(activeTab) {
      case 'account': return 'Account';
      case 'staking': return 'Staking';
      case 'usdto': return 'USDTO';
      case 'voting': return 'Voting';
      case 'management': return 'Management';
      default: return 'Account';
    }
  };
  return (
    <main className="flex-1 bg-gray-50 py-8 md:py-20">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-2xl md:text-4xl font-bold text-gray-900 mb-8 md:mb-12">
            Welcome to the Flare portal!
          </h1>

          <div className="bg-gray-800 text-white p-4 md:p-8 rounded-lg max-w-4xl mx-auto">
            <div className="space-y-4 md:space-y-6 text-left">
              <p className="text-sm md:text-base leading-relaxed">
                Here, you can manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers
                and thereby contribute to the networks' decentralization and stability. You can also participate in governance voting from this
                portal.
              </p>

              <p className="text-sm md:text-base leading-relaxed">
                Please start by clicking on the 'Connect to Wallet' button, select your wallet and follow the instructions. This page is compatible
                for both desktop and mobile browsers. For technical support, please make a request in the General room on our{' '}
                <a href="#" className="text-red-400 hover:text-red-300 underline">
                  Discord
                </a>{' '}
                or on our{' '}
                <a href="#" className="text-red-400 hover:text-red-300 underline">
                  Telegram
                </a>.
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default WelcomeSection;
