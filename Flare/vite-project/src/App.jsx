import { useState } from 'react'
import Header from './components/Header'
import WelcomeSection from './components/WelcomeSection'
import VotingSection from './components/VotingSection'
import ManagementSection from './components/ManagementSection'
import Footer from './components/Footer'

function App() {
  const [activeTab, setActiveTab] = useState('account')

  const renderContent = () => {
    switch(activeTab) {
      case 'voting':
        return <VotingSection />
      case 'management':
        return <ManagementSection />
      case 'account':
      case 'staking':
      case 'usdto':
      default:
        return <WelcomeSection activeTab={activeTab} />
    }
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header activeTab={activeTab} setActiveTab={setActiveTab} />
      {renderContent()}
      <Footer />
    </div>
  )
}

export default App
