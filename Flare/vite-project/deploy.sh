#!/bin/bash

# Flare Frontend Deployment Script for Firebase
# Usage: ./deploy.sh [backend-url]
# Example: ./deploy.sh https://flare-backend-xxxx.onrender.com

set -e

BACKEND_URL=${1}
PROJECT_NAME="flare-portal-app"

echo "🔥 Deploying Flare Frontend to Firebase"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Installing..."
    npm install -g firebase-tools
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please login to Firebase..."
    firebase login
fi

# Update backend URL if provided
if [ ! -z "$BACKEND_URL" ]; then
    echo "🔧 Updating backend URL to: $BACKEND_URL"
    echo "VITE_API_BASE_URL=$BACKEND_URL" > .env.production
else
    echo "⚠️  No backend URL provided. Using existing .env.production"
    if [ ! -f .env.production ]; then
        echo "❌ .env.production not found. Please provide backend URL or create the file."
        echo "Usage: ./deploy.sh https://your-render-backend-url.onrender.com"
        exit 1
    fi
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project
echo "🏗️  Building project for production..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed. dist directory not found."
    exit 1
fi

# Initialize Firebase if not already done
if [ ! -f "firebase.json" ]; then
    echo "🔥 Initializing Firebase..."
    firebase init hosting --project $PROJECT_NAME
fi

# Deploy to Firebase
echo "🚀 Deploying to Firebase Hosting..."
firebase deploy --project $PROJECT_NAME

# Get the hosting URL
HOSTING_URL=$(firebase hosting:sites:list --project $PROJECT_NAME --json | grep -o '"url":"[^"]*' | cut -d'"' -f4 | head -1)

echo ""
echo "✅ Deployment successful!"
echo "🌐 Frontend URL: $HOSTING_URL"
echo "🔗 Backend URL: $(cat .env.production | grep VITE_API_BASE_URL | cut -d'=' -f2)"
echo ""
echo "📝 Next steps:"
echo "1. Update your Render backend FRONTEND_URL environment variable to: $HOSTING_URL"
echo "2. Test the wallet connection flow"
echo "3. Configure custom domain (optional)"
echo ""
echo "🧪 Test your deployment:"
echo "curl $HOSTING_URL"
